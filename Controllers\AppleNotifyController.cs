using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Cryptography.X509Certificates;
using Microsoft.IdentityModel.Tokens;

namespace AppleNotificationApi.Controllers
{
    public class AppleNotifyController : ApiController
    {
        [HttpPost]
        [AllowAnonymous]
        [Route("AppleNotify")]
        public async Task<IHttpActionResult> AppleNotify([FromBody] JObject body)
        {
            try
            {
                var signedPayload = body["signedPayload"]?.ToString();
                if (string.IsNullOrEmpty(signedPayload))
                    return BadRequest("signedPayload is required.");

                var payload = DecodeAndValidateJws(signedPayload);
                var signedTransactionInfo = payload["data"]?["signedTransactionInfo"]?.ToString();
                var transactionInfo = DecodeAndValidateJws(signedTransactionInfo);

                JObject renewalInfo = null;
                var signedRenewalInfo = payload["data"]?["signedRenewalInfo"]?.ToString();
                if (!string.IsNullOrEmpty(signedRenewalInfo))
                {
                    renewalInfo = DecodeAndValidateJws(signedRenewalInfo);
                }

                var logObject = new JObject
                {
                    ["rootPayload"] = payload,
                    ["transactionInfo"] = transactionInfo,
                    ["renewalInfo"] = renewalInfo
                };

                await LogToDatabase(logObject.ToString(Formatting.Indented));
                return Ok();
            }
            catch (Exception ex)
            {
                await LogToDatabase("AppleNotify Exception: " + ex.ToString());
                return InternalServerError(ex);
            }
        }

        private async Task LogToDatabase(string json)
        {
            using (SqlConnection conn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["DBDiginotice"].ConnectionString))
            using (SqlCommand cmd = new SqlCommand("Usp_dump_applenotification", conn))
            {
                cmd.CommandType = CommandType.StoredProcedure;
                cmd.Parameters.AddWithValue("@json", json);
                await conn.OpenAsync();
                await cmd.ExecuteNonQueryAsync();
            }
        }

        private JObject DecodeAndValidateJws(string jws)
        {
            if (string.IsNullOrWhiteSpace(jws))
                throw new ArgumentException("JWS is null or empty.");

            var parts = jws.Split('.');
            if (parts.Length != 3)
                throw new Exception("Invalid JWS format. Expected 3 parts.");

            var securityKey = ExtractPublicKeyFromX5C(jws);

            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters
            {
                RequireExpirationTime = false,
                RequireSignedTokens = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = securityKey
            };

            tokenHandler.ValidateToken(jws, validationParameters, out var validatedToken);
            var jwtToken = (JwtSecurityToken)validatedToken;

            return JObject.Parse(jwtToken.Payload.SerializeToJson());
        }

        private SecurityKey ExtractPublicKeyFromX5C(string jws)
        {
            var headerPart = jws.Split('.')[0];
            var headerJson = Encoding.UTF8.GetString(Base64UrlDecode(headerPart));
            var header = JObject.Parse(headerJson);
            var x5cArray = header["x5c"] as JArray;

            if (x5cArray == null || x5cArray.Count == 0)
                throw new Exception("Missing x5c certificate in JWS header.");

            string certBase64 = x5cArray[0].ToString();
            var certBytes = Convert.FromBase64String(certBase64);
            var cert = new X509Certificate2(certBytes);
            var rsaPublicKey = cert.GetRSAPublicKey();

            return new RsaSecurityKey(rsaPublicKey);
        }

        private byte[] Base64UrlDecode(string input)
        {
            string padded = input.Length % 4 == 0
                ? input
                : input.Length % 4 == 2 ? input + "=="
                : input.Length % 4 == 3 ? input + "="
                : throw new ArgumentException("Illegal base64url string!");

            return Convert.FromBase64String(padded.Replace('-', '+').Replace('_', '/'));
        }
    }
}
