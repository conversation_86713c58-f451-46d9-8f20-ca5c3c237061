using System;
using System.Data;
using System.Data.SqlClient;
using System.Text;
using System.Threading.Tasks;
using System.Web.Http;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Cryptography.X509Certificates;
using Microsoft.IdentityModel.Tokens;

namespace AppleNotificationApi.Controllers
{
    public class AppleNotifyController : ApiController
    {
        // Apple App Store Connect API Configuration
        private readonly string _keyId = "R5Z242F048";
        private readonly string _teamId = "5F4RR7TVX6";
        private readonly string _bundleId = "com.uvktech.diginotice";
        private readonly string _privateKeyPath = "~/App_Data/AuthKey_R5Z242F048.p8";

        /// <summary>
        /// Comprehensive Apple Notification API Handler
        /// This single method handles all Apple notification processing including:
        /// - JWT validation and decoding
        /// - Transaction info extraction
        /// - Renewal info processing
        /// - Database logging
        /// </summary>
        [HttpPost]
        [AllowAnonymous]
        [Route("AppleNotify")]
        public async Task<IHttpActionResult> AppleNotify([FromBody] JObject body)
        {
            try
            {
                // Step 1: Validate input
                var signedPayload = body["signedPayload"]?.ToString();
                if (string.IsNullOrEmpty(signedPayload))
                    return BadRequest("signedPayload is required.");

                // Step 2: Decode and validate the main signed payload
                var payload = DecodeAndValidateJws(signedPayload);

                // Step 3: Extract and decode transaction information
                var signedTransactionInfo = payload["data"]?["signedTransactionInfo"]?.ToString();
                var transactionInfo = DecodeAndValidateJws(signedTransactionInfo);

                // Step 4: Extract and decode renewal information (if present)
                JObject renewalInfo = null;
                var signedRenewalInfo = payload["data"]?["signedRenewalInfo"]?.ToString();
                if (!string.IsNullOrEmpty(signedRenewalInfo))
                {
                    renewalInfo = DecodeAndValidateJws(signedRenewalInfo);
                }

                // Step 5: Create comprehensive log object
                var logObject = new JObject
                {
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    ["rootPayload"] = payload,
                    ["transactionInfo"] = transactionInfo,
                    ["renewalInfo"] = renewalInfo,
                    ["notificationType"] = payload["notificationType"]?.ToString(),
                    ["subtype"] = payload["subtype"]?.ToString(),
                    ["notificationUUID"] = payload["notificationUUID"]?.ToString(),
                    ["version"] = payload["version"]?.ToString()
                };

                // Step 6: Log to database
                await LogToDatabase(logObject.ToString(Formatting.Indented));

                // Step 7: Return success response
                return Ok(new {
                    success = true,
                    message = "Apple notification processed successfully",
                    notificationUUID = payload["notificationUUID"]?.ToString()
                });
            }
            catch (Exception ex)
            {
                // Log exception details
                var errorLog = new JObject
                {
                    ["timestamp"] = DateTime.UtcNow.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                    ["error"] = "AppleNotify Exception",
                    ["message"] = ex.Message,
                    ["stackTrace"] = ex.StackTrace,
                    ["innerException"] = ex.InnerException?.Message
                };

                await LogToDatabase(errorLog.ToString(Formatting.Indented));
                return InternalServerError(ex);
            }
        }

        /// <summary>
        /// Logs data to the database using stored procedure
        /// </summary>
        private async Task LogToDatabase(string json)
        {
            using var conn = new SqlConnection(System.Configuration.ConfigurationManager.ConnectionStrings["DBDiginotice"].ConnectionString);
            using var cmd = new SqlCommand("Usp_dump_applenotification", conn)
            {
                CommandType = CommandType.StoredProcedure
            };

            cmd.Parameters.AddWithValue("@json", json);
            await conn.OpenAsync();
            await cmd.ExecuteNonQueryAsync();
        }

        /// <summary>
        /// Decodes and validates Apple JWS (JSON Web Signature) tokens
        /// Handles both signed payload and transaction/renewal info
        /// </summary>
        private JObject DecodeAndValidateJws(string jws)
        {
            if (string.IsNullOrWhiteSpace(jws))
                throw new ArgumentException("JWS is null or empty.", nameof(jws));

            var parts = jws.Split('.');
            if (parts.Length != 3)
                throw new ArgumentException("Invalid JWS format. Expected 3 parts separated by dots.", nameof(jws));

            // Extract and validate the public key from the certificate
            var securityKey = ExtractPublicKeyFromX5C(jws);

            // Configure JWT validation parameters for Apple's specific requirements
            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters
            {
                RequireExpirationTime = false,
                RequireSignedTokens = true,
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = false,
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = securityKey,
                ClockSkew = TimeSpan.Zero
            };

            // Validate the token and extract payload
            tokenHandler.ValidateToken(jws, validationParameters, out var validatedToken);
            var jwtToken = (JwtSecurityToken)validatedToken;

            return JObject.Parse(jwtToken.Payload.SerializeToJson());
        }

        /// <summary>
        /// Extracts the RSA public key from the x5c certificate chain in the JWS header
        /// </summary>
        private SecurityKey ExtractPublicKeyFromX5C(string jws)
        {
            var headerPart = jws.Split('.')[0];
            var headerJson = Encoding.UTF8.GetString(Base64UrlDecode(headerPart));
            var header = JObject.Parse(headerJson);

            // Use pattern matching as suggested by IDE
            if (header["x5c"] is not JArray x5cArray || x5cArray.Count == 0)
                throw new ArgumentException("Missing or empty x5c certificate chain in JWS header.");

            // Extract the first certificate from the chain
            string certBase64 = x5cArray[0].ToString();
            var certBytes = Convert.FromBase64String(certBase64);
            var cert = new X509Certificate2(certBytes);

            // Verify this is an Apple certificate (optional additional validation)
            if (!cert.Subject.Contains("Apple"))
                throw new SecurityTokenValidationException("Certificate is not from Apple.");

            var rsaPublicKey = cert.GetRSAPublicKey()
                ?? throw new ArgumentException("Unable to extract RSA public key from certificate.");

            return new RsaSecurityKey(rsaPublicKey);
        }

        /// <summary>
        /// Decodes Base64URL encoded strings (used in JWT)
        /// </summary>
        private static byte[] Base64UrlDecode(string input)
        {
            if (string.IsNullOrEmpty(input))
                throw new ArgumentException("Input cannot be null or empty.", nameof(input));

            // Add padding if necessary
            string padded = input.Length % 4 switch
            {
                0 => input,
                2 => input + "==",
                3 => input + "=",
                _ => throw new ArgumentException("Invalid base64url string length.", nameof(input))
            };

            // Replace URL-safe characters with standard Base64 characters
            return Convert.FromBase64String(padded.Replace('-', '+').Replace('_', '/'));
        }
    }
}
